import React from 'react';

interface GeometricShape {
  color: string;
  position: { x: number; y: number };
  size: number;
  rotation: number;
  shape: 'square' | 'diamond' | 'circle';
}

const GeometricHeader: React.FC = () => {
  // Define the geometric shapes matching the reference design
  const shapes: GeometricShape[] = [
    // Top row
    { color: 'hsl(330, 81%, 60%)', position: { x: 0, y: 0 }, size: 60, rotation: 0, shape: 'square' },
    { color: 'hsl(203, 89%, 53%)', position: { x: 0, y: 0 }, size: 60, rotation: 45, shape: 'square' },
    { color: 'hsl(271, 81%, 56%)', position: { x: 0, y: 0 }, size: 60, rotation: 45, shape: 'diamond' },
    { color: 'hsl(142, 76%, 36%)', position: { x: 0, y: 0 }, size: 60, rotation: 45, shape: 'diamond' },

    // Bottom row
    { color: 'hsl(203, 89%, 53%)', position: { x: 0, y: 0 }, size: 60, rotation: 45, shape: 'diamond' },
    { color: 'hsl(48, 96%, 53%)', position: { x: 0, y: 0 }, size: 60, rotation: 0, shape: 'circle' },
    { color: 'hsl(348, 83%, 47%)', position: { x: 0, y: 0 }, size: 60, rotation: 0, shape: 'square' },
    { color: 'hsl(25, 95%, 53%)', position: { x: 0, y: 0 }, size: 60, rotation: 0, shape: 'circle' },
  ];

  const renderShape = (shape: GeometricShape, index: number) => {
    const baseClasses = "transition-transform duration-300 hover:scale-110";
    const shapeStyle = {
      width: `${shape.size}px`,
      height: `${shape.size}px`,
      backgroundColor: shape.color,
      transform: `rotate(${shape.rotation}deg)`,
    };

    switch (shape.shape) {
      case 'circle':
        return (
          <div
            key={index}
            className={`${baseClasses} rounded-full`}
            style={shapeStyle}
          />
        );
      case 'diamond':
        return (
          <div
            key={index}
            className={`${baseClasses} rounded-lg`}
            style={{
              ...shapeStyle,
              transform: `rotate(45deg) rotate(${shape.rotation}deg)`,
            }}
          />
        );
      case 'square':
      default:
        return (
          <div
            key={index}
            className={`${baseClasses} rounded-lg`}
            style={shapeStyle}
          />
        );
    }
  };

  return (
    <div className="relative w-full h-48 bg-black overflow-hidden">
      {/* Geometric pattern container */}
      <div className="relative w-full h-full flex items-center justify-center">
        <div className="grid grid-cols-4 gap-2 p-4">
          {shapes.map((shape, index) => renderShape(shape, index))}
        </div>
      </div>

      {/* Overlay gradient for better visual effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/30" />
    </div>
  );
};

export default GeometricHeader;
