import { useState } from "react";
import { Menu, X, Home, User, Briefcase, BookOpen, FileText, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";

const navigation = [
  { name: "Home", icon: Home, href: "#home" },
  { name: "About", icon: User, href: "#about" },
  { name: "Projects", icon: Briefcase, href: "#projects" },
  { name: "Blog", icon: BookOpen, href: "#blog" },
  { name: "Resume", icon: FileText, href: "#resume" },
  { name: "Contact", icon: Mail, href: "#contact" },
];

const scrollToSection = (href: string) => {
  const element = document.querySelector(href);
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }
};

export default function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="lg:hidden">
      {/* Mobile Header */}
      <header className="fixed top-0 left-0 right-0 bg-background/95 backdrop-blur-sm border-b border-border z-50 p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold">Pratik Jadhav</h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="p-2 rounded-xl hover:bg-muted transition-all duration-200"
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black/50" onClick={() => setIsOpen(false)} />
          <div className="fixed left-0 top-0 h-full w-80 bg-background/95 backdrop-blur-sm border-r border-border p-6 shadow-xl">
            <div className="flex items-center justify-between mb-10">
              <h1 className="text-xl font-bold">Pratik Jadhav</h1>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="p-2 rounded-xl hover:bg-muted transition-all duration-200"
              >
                <X className="h-6 w-6" />
              </Button>
            </div>

            <nav className="space-y-3">
              {navigation.map((item) => (
                <button
                  key={item.name}
                  onClick={() => {
                    scrollToSection(item.href);
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-foreground hover:bg-muted/80 transition-all duration-200 hover:shadow-md transform hover:scale-105"
                >
                  <item.icon className="h-6 w-6 text-primary" />
                  <span className="text-lg font-medium">{item.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>
      )}
    </div>
  );
}