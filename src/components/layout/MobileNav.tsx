import { useState } from "react";
import { Menu, X, Home, Search, Bell, Mail, Bookmark, User, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";

const navigation = [
  { name: "Home", icon: Home, href: "#home" },
  { name: "Explore", icon: Search, href: "#projects" },
  { name: "Notifications", icon: Bell, href: "#contact" },
  { name: "Messages", icon: Mail, href: "#contact" },
  { name: "Bookmarks", icon: Bookmark, href: "#blog" },
  { name: "Profile", icon: User, href: "#about" },
  { name: "More", icon: MoreHorizontal, href: "#resume" },
];

const scrollToSection = (href: string) => {
  const element = document.querySelector(href);
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }
};

export default function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="lg:hidden">
      {/* Mobile Header */}
      <header className="fixed top-0 left-0 right-0 bg-black/95 backdrop-blur-sm border-b border-border z-50 p-4">
        <div className="flex items-center justify-between">
          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
            <span className="text-black font-bold text-lg">𝕏</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="p-2 rounded-xl hover:bg-white/10 transition-all duration-200 text-white"
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black/50" onClick={() => setIsOpen(false)} />
          <div className="fixed left-0 top-0 h-full w-80 bg-black border-r border-border p-6">
            <div className="flex items-center justify-between mb-10">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <span className="text-black font-bold text-lg">𝕏</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="p-2 rounded-xl hover:bg-white/10 transition-all duration-200 text-white"
              >
                <X className="h-6 w-6" />
              </Button>
            </div>

            <nav className="space-y-1">
              {navigation.map((item) => (
                <button
                  key={item.name}
                  onClick={() => {
                    scrollToSection(item.href);
                    setIsOpen(false);
                  }}
                  className="twitter-nav-item w-full"
                >
                  <item.icon className="h-7 w-7" />
                  <span className="text-xl">{item.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>
      )}
    </div>
  );
}