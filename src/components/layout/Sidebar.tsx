import {
  Home,
  Search,
  Bell,
  Mail,
  Bookmark,
  User,
  MoreHorizontal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";

// Twitter-style navigation matching the reference design
const navigation = [
  { name: "Home", icon: Home, path: "/" },
  { name: "Explore", icon: Search, path: "/explore" },
  { name: "Notifications", icon: Bell, path: "/messages" },
  { name: "Messages", icon: Mail, path: "/messages" },
  { name: "Bookmarks", icon: Bookmark, path: "/bookmarks" },
  { name: "Profile", icon: User, path: "/profile" },
  { name: "More", icon: MoreHorizontal, path: "/profile" },
];

export default function Sidebar() {
  const navigate = useNavigate();
  const location = useLocation();

  // Force dark theme to match reference design
  useEffect(() => {
    document.documentElement.classList.add('dark');
    localStorage.setItem('theme', 'dark');
  }, []);

  const handleNavClick = (item: typeof navigation[0]) => {
    navigate(item.path);
  };

  const isActive = (path: string) => {
    if (path === "/" && location.pathname === "/") return true;
    if (path !== "/" && location.pathname.startsWith(path)) return true;
    return false;
  };

  return (
    <div className="hidden lg:flex fixed left-0 top-0 h-full w-64 bg-black border-r border-border flex-col px-6 py-4">
      {/* Twitter Logo */}
      <div className="mb-8 mt-2">
        <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
          <span className="text-black font-bold text-lg">𝕏</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1">
        {navigation.map((item, index) => (
          <motion.button
            key={item.name}
            onClick={() => handleNavClick(item)}
            className={`twitter-nav-item w-full ${
              isActive(item.path) ? 'active' : ''
            }`}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            whileHover={{
              scale: 1.02,
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              transition: { duration: 0.2 }
            }}
            whileTap={{ scale: 0.98 }}
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <item.icon className="h-7 w-7" />
            </motion.div>
            <span className="text-xl">{item.name}</span>
          </motion.button>
        ))}
      </nav>

      {/* Tweet Button */}
      <div className="mb-6">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button className="twitter-button w-full py-4 text-lg font-bold">
            Tweet
          </Button>
        </motion.div>
      </div>

      {/* Profile Section */}
      <div className="flex items-center space-x-3 p-3 rounded-full hover:bg-white/10 transition-colors duration-200 cursor-pointer">
        <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
          PJ
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-white font-bold text-sm truncate">Pratik Jadhav</p>
          <p className="text-gray-500 text-sm truncate">@PratikJH153</p>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-500" />
      </div>
    </div>
  );
}