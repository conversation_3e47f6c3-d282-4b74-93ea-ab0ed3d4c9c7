import {
  Home,
  Search,
  Bell,
  Mail,
  Bookmark,
  User,
  MoreHorizontal,
  Github,
  Linkedin,
  Briefcase,
  FileText
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

// Twitter-style navigation matching the reference design
const navigation = [
  { name: "Home", icon: Home, href: "#home", active: true },
  { name: "Explore", icon: Search, href: "#projects" },
  { name: "Notifications", icon: Bell, href: "#contact" },
  { name: "Messages", icon: Mail, href: "#contact" },
  { name: "Bookmarks", icon: Bookmark, href: "#blog" },
  { name: "Profile", icon: User, href: "#about" },
  { name: "More", icon: MoreHorizontal, href: "#resume" },
];

const scrollToSection = (href: string) => {
  const element = document.querySelector(href);
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }
};

export default function Sidebar() {
  const [activeItem, setActiveItem] = useState("Home");

  // Force dark theme to match reference design
  useEffect(() => {
    document.documentElement.classList.add('dark');
    localStorage.setItem('theme', 'dark');
  }, []);

  const handleNavClick = (item: typeof navigation[0]) => {
    setActiveItem(item.name);
    scrollToSection(item.href);
  };

  return (
    <div className="hidden lg:flex fixed left-0 top-0 h-full w-64 bg-black border-r border-border flex-col px-6 py-4">
      {/* Twitter Logo */}
      <div className="mb-8 mt-2">
        <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
          <span className="text-black font-bold text-lg">𝕏</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1">
        {navigation.map((item) => (
          <button
            key={item.name}
            onClick={() => handleNavClick(item)}
            className={`twitter-nav-item w-full ${
              activeItem === item.name ? 'active' : ''
            }`}
          >
            <item.icon className="h-7 w-7" />
            <span className="text-xl">{item.name}</span>
          </button>
        ))}
      </nav>

      {/* Tweet Button */}
      <div className="mb-6">
        <Button className="twitter-button w-full py-4 text-lg font-bold">
          Tweet
        </Button>
      </div>

      {/* Profile Section */}
      <div className="flex items-center space-x-3 p-3 rounded-full hover:bg-white/10 transition-colors duration-200 cursor-pointer">
        <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
          PJ
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-white font-bold text-sm truncate">Pratik Jadhav</p>
          <p className="text-gray-500 text-sm truncate">@PratikJH153</p>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-500" />
      </div>
    </div>
  );
}