import { Home, User, Briefcase, BookOpen, FileText, Mail, Github, Linkedin, Sun, Moon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

const navigation = [
  { name: "Home", icon: Home, href: "#home" },
  { name: "About", icon: User, href: "#about" },
  { name: "Projects", icon: Briefcase, href: "#projects" },
  { name: "Blog", icon: BookOpen, href: "#blog" },
  { name: "Resume", icon: FileText, href: "#resume" },
  { name: "Contact", icon: Mail, href: "#contact" },
];

const scrollToSection = (href: string) => {
  const element = document.querySelector(href);
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }
};

const socialLinks = [
  { name: "GitHub", icon: Github, href: "https://github.com/PratikJH153" },
  { name: "LinkedIn", icon: Linkedin, href: "https://linkedin.com/in/pratikjh" },
];

export default function Sidebar() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const theme = localStorage.getItem('theme');
    if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    if (newTheme) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  return (
    <div className="hidden lg:flex fixed left-0 top-0 h-full w-64 bg-background/95 backdrop-blur-sm border-r border-border flex-col p-6 shadow-xl">
      {/* Logo */}
      <div className="mb-10">
        <h1 className="text-2xl font-bold text-foreground mb-2">Pratik Jadhav</h1>
        <p className="text-sm text-muted-foreground">Creative Technologist</p>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-3">
        {navigation.map((item) => (
          <button
            key={item.name}
            onClick={() => scrollToSection(item.href)}
            className="w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-foreground hover:bg-muted/80 transition-all duration-200 group hover:shadow-md transform hover:scale-105"
          >
            <item.icon className="h-6 w-6 group-hover:scale-110 transition-transform duration-200 text-primary" />
            <span className="text-lg font-medium">{item.name}</span>
          </button>
        ))}
      </nav>

      {/* Social Links */}
      <div className="space-y-3 mb-6">
        {socialLinks.map((link) => (
          <a
            key={link.name}
            href={link.href}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-4 px-4 py-3 rounded-xl text-muted-foreground hover:text-foreground hover:bg-muted/80 transition-all duration-200 group hover:shadow-md transform hover:scale-105"
          >
            <link.icon className="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-lg font-medium">{link.name}</span>
          </a>
        ))}
      </div>

      {/* Theme Toggle */}
      <Button
        variant="outline"
        size="sm"
        onClick={toggleTheme}
        className="flex items-center space-x-3 self-start px-4 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
      >
        {isDark ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
        <span className="font-medium">{isDark ? 'Light' : 'Dark'}</span>
      </Button>
    </div>
  );
}