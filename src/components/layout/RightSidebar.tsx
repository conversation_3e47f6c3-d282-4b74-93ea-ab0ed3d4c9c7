import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Search, TrendingUp, Users, Code, Briefcase } from "lucide-react";

interface SuggestionItem {
  id: string;
  name: string;
  handle: string;
  description: string;
  avatar: string;
  verified?: boolean;
}

interface TrendingItem {
  id: string;
  category: string;
  title: string;
  posts: string;
}

const RightSidebar: React.FC = () => {
  const suggestions: SuggestionItem[] = [
    {
      id: "1",
      name: "Codrops",
      handle: "@codrops",
      description: "Web design and development",
      avatar: "C",
    },
    {
      id: "2",
      name: "Webdesigner Depot",
      handle: "@webdesignerdepot",
      description: "Design inspiration",
      avatar: "W",
    },
    {
      id: "3",
      name: "Awwwards",
      handle: "@awwwards",
      description: "Website awards",
      avatar: "A",
    },
  ];

  const trending: TrendingItem[] = [
    {
      id: "1",
      category: "Technology",
      title: "Apple",
      posts: "42 minutes ago",
    },
    {
      id: "2", 
      category: "Trending in Music",
      title: "Vanilla Ice",
      posts: "59.3K Tweets",
    },
    {
      id: "3",
      category: "Sports · Entertainment", 
      title: "<PERSON> Ross",
      posts: "59.1K Tweets",
    },
    {
      id: "4",
      category: "Technology",
      title: "React",
      posts: "25.2K Tweets",
    },
  ];

  return (
    <div className="hidden xl:flex fixed right-0 top-0 h-full w-80 bg-black border-l border-border flex-col p-4 space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
        <input
          type="text"
          placeholder="Search Twitter"
          className="w-full bg-gray-900 text-white placeholder-gray-500 rounded-full py-3 pl-12 pr-4 border-none outline-none focus:bg-gray-800 transition-colors duration-200"
        />
      </div>

      {/* You might like */}
      <Card className="twitter-card bg-gray-900 border-gray-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-bold text-white">You might like</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {suggestions.map((suggestion) => (
            <div key={suggestion.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
                  {suggestion.avatar}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-white font-bold text-sm truncate">{suggestion.name}</p>
                  <p className="text-gray-500 text-sm truncate">{suggestion.handle}</p>
                </div>
              </div>
              <Button className="twitter-button-outline px-4 py-1 text-sm">
                Follow
              </Button>
            </div>
          ))}
          <button className="text-primary text-sm hover:underline">
            Show more
          </button>
        </CardContent>
      </Card>

      {/* What's happening */}
      <Card className="twitter-card bg-gray-900 border-gray-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-bold text-white">What's happening</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {trending.map((item) => (
            <div key={item.id} className="hover:bg-white/5 p-2 rounded cursor-pointer transition-colors duration-200">
              <p className="text-gray-500 text-sm">{item.category}</p>
              <p className="text-white font-bold">{item.title}</p>
              <p className="text-gray-500 text-sm">{item.posts}</p>
            </div>
          ))}
          <button className="text-primary text-sm hover:underline">
            Show more
          </button>
        </CardContent>
      </Card>

      {/* Portfolio specific section */}
      <Card className="twitter-card bg-gray-900 border-gray-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-bold text-white flex items-center space-x-2">
            <Code className="h-5 w-5" />
            <span>Tech Stack</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {["React", "TypeScript", "Python", "Node.js", "AI/ML", "Docker"].map((tech) => (
            <div key={tech} className="inline-block bg-primary/20 text-primary px-3 py-1 rounded-full text-sm mr-2 mb-2">
              {tech}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Contact section */}
      <Card className="twitter-card bg-gray-900 border-gray-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-bold text-white flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>Get in Touch</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <p className="text-gray-400 text-sm">
            Interested in collaboration? Let's connect!
          </p>
          <div className="space-y-2">
            <a
              href="mailto:<EMAIL>"
              className="block text-primary hover:underline text-sm"
            >
              <EMAIL>
            </a>
            <a
              href="https://linkedin.com/in/pratikjh"
              target="_blank"
              rel="noopener noreferrer"
              className="block text-primary hover:underline text-sm"
            >
              LinkedIn Profile
            </a>
            <a
              href="https://github.com/PratikJH153"
              target="_blank"
              rel="noopener noreferrer"
              className="block text-primary hover:underline text-sm"
            >
              GitHub Profile
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RightSidebar;
