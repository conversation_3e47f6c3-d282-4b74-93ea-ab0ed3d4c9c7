import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Sidebar from './Sidebar';
import RightSidebar from './RightSidebar';
import MobileNav from './MobileNav';
import ScrollToTop from '../ui/ScrollToTop';

const Layout: React.FC = () => {
  const location = useLocation();

  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20,
    },
    in: {
      opacity: 1,
      y: 0,
    },
    out: {
      opacity: 0,
      y: -20,
    },
  };

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.4,
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Mobile Navigation */}
      <MobileNav />

      {/* Desktop Layout - Three Column Twitter-like */}
      <div className="hidden lg:flex">
        {/* Left Sidebar - Navigation */}
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <Sidebar />
        </motion.div>

        {/* Main Content Area - Center column */}
        <div className="flex-1 ml-64 xl:mr-80 border-x border-border">
          {/* Twitter-style Header */}
          <motion.div
            className="sticky top-0 bg-black/80 backdrop-blur-md border-b border-border z-10"
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="flex items-center justify-between px-4 py-3">
              <div className="flex items-center space-x-4">
                <div>
                  <h1 className="text-xl font-bold text-white">unfold</h1>
                  <p className="text-sm text-gray-500">@PratikJH153</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Page Content with Animations */}
          <AnimatePresence mode="wait">
            <motion.div
              key={location.pathname}
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
            >
              <Outlet />
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Right Sidebar - Suggestions and trending */}
        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
        >
          <RightSidebar />
        </motion.div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden pt-16">
        <div className="w-full">
          <AnimatePresence mode="wait">
            <motion.div
              key={location.pathname}
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
            >
              <Outlet />
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
};

export default Layout;
