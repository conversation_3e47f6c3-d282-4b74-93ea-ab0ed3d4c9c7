import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { MapPin, Calendar, ExternalLink, Terminal, Send } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const techStack = [
  "Python", "C++", "TypeScript", "JavaScript", "Dart", "Flutter", 
  "Node.js", "NestJS", "LangChain", "Pandas", "NumPy", "PyTorch",
  "MongoDB", "MySQL", "Neo4j", "Redis", "Firebase", "Docker", 
  "Git", "Google Cloud Platform", "N8N"
];

const featuredProject = {
  title: "Vibeo - AI-Powered Nightlife Discovery",
  description: "Co-founded and architected an AI-powered mobile app for personalized nightlife video discovery using Flutter, OpenAI Vision, and GCP.",
  tech: ["Flutter", "Python", "OpenAI Vision", "GCP", "Pinecone", "LangChain"],
  demoUrl: "https://www.vibeo.io"
};

export default function ProfileSection() {
  const form = useForm({
    defaultValues: {
      email: "",
      message: ""
    }
  });

  const onSubmit = (data: any) => {
    console.log("Contact form submitted:", data);
    toast.success("Message sent! I'll get back to you soon.");
    form.reset();
  };

  return (
    <div className="space-y-6">
      {/* Profile Summary */}
      <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0 bg-gradient-to-br from-background to-muted/20">
        <CardContent className="p-8">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6 mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary/60 rounded-2xl flex items-center justify-center text-white font-bold text-2xl font-mono shadow-lg">
              <Terminal className="h-10 w-10" />
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-2xl font-mono mb-1">~/pratik-dev</h3>
              <p className="text-muted-foreground font-mono text-sm mb-2">$ whoami</p>
              <p className="text-lg text-muted-foreground">Creative Technologist | AI/ML Engineer</p>
            </div>
          </div>
          
          <div className="bg-muted/30 rounded-xl p-4 mb-6 font-mono text-sm border border-muted">
            <p className="text-green-400 mb-2 font-semibold">$ cat about.txt</p>
            <p className="text-muted-foreground leading-relaxed">
              MS Computer Science student at Illinois Institute of Technology specializing in AI/ML. 
              Co-founder & CTO at Vibeo Inc. Passionate about building AI-powered solutions.
            </p>
          </div>
          
          <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-primary" />
              <span>Chicago, IL</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-primary" />
              <span>Graduating May 2025</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Now Working On */}
      <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl flex items-center space-x-2">
            <span className="text-green-400">●</span>
            <span>Now Working On</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/30 rounded-xl p-4 font-mono text-sm border border-muted">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-green-400 text-lg">→</span>
              <span className="font-medium font-mono">AI Agent Code Reviewer</span>
            </div>
            <p className="text-muted-foreground pl-6 leading-relaxed">
              Building an AI agent that connects to repositories and enables conversational code review via LLM chat interface.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Tech Stack */}
      <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl">Tech Stack</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {techStack.map((tech) => (
              <Badge 
                key={tech} 
                variant="secondary" 
                className="hover:bg-primary hover:text-primary-foreground transition-all duration-200 cursor-default px-3 py-1 rounded-full shadow-sm hover:shadow-md transform hover:scale-105"
              >
                {tech}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Featured Project */}
      <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl">Featured Project</CardTitle>
        </CardHeader>
        <CardContent>
          <h4 className="font-semibold text-lg mb-3">{featuredProject.title}</h4>
          <p className="text-muted-foreground mb-4 leading-relaxed">
            {featuredProject.description}
          </p>
          
          <div className="flex flex-wrap gap-2 mb-4">
            {featuredProject.tech.map((tech) => (
              <Badge key={tech} variant="outline" className="text-xs rounded-full">
                {tech}
              </Badge>
            ))}
          </div>
          
          <Button 
            size="sm" 
            className="w-full rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            onClick={() => window.open(featuredProject.demoUrl, '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            View Project
          </Button>
        </CardContent>
      </Card>

      {/* Quick Contact */}
      <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-mono">$ send_message</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/30 rounded-xl p-4 mb-6 font-mono text-sm border border-muted">
            <p className="text-green-400 mb-2 font-semibold">// Drop me a line!</p>
            <p className="text-muted-foreground leading-relaxed">
              Interested in AI/ML projects or collaboration? Let's connect! Email: <EMAIL>
            </p>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-mono text-sm">email:</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email"
                        className="font-mono text-sm rounded-xl border-muted focus:border-primary transition-colors"
                        required
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-mono text-sm">message:</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Hello! I'd like to connect..."
                        className="min-h-[100px] font-mono text-sm resize-none rounded-xl border-muted focus:border-primary transition-colors"
                        required
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button 
                type="submit" 
                className="w-full font-mono rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
              >
                <Send className="h-4 w-4 mr-2" />
                send --now
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
