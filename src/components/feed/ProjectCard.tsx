import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heart, MessageCircle, Share, ExternalLink, Github } from "lucide-react";
import { useState } from "react";

interface ProjectCardProps {
  project: {
    id: string;
    title: string;
    description: string;
    image: string;
    tech: string[];
    githubUrl?: string;
    demoUrl?: string;
    likes: number;
    comments: number;
    timeAgo: string;
  };
}

export default function ProjectCard({ project }: ProjectCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(project.likes);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  return (
    <Card className="shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group rounded-xl border-0 bg-gradient-to-br from-background to-muted/10">
      <CardContent className="p-8">
        {/* Header */}
        <div className="flex items-start space-x-4 mb-6">
          <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-md">
            PJ
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-semibold text-lg">Pratik Jadhav</span>
              <span className="text-muted-foreground">·</span>
              <span className="text-muted-foreground text-sm">{project.timeAgo}</span>
            </div>
            <p className="text-muted-foreground text-sm">@PratikJH153</p>
          </div>
        </div>

        {/* Content */}
        <div className="mb-6">
          <h3 className="text-2xl font-bold mb-3 group-hover:text-primary transition-colors duration-200">
            {project.title}
          </h3>
          <p className="text-foreground mb-6 leading-relaxed text-lg">{project.description}</p>

          {/* Tech Stack */}
          <div className="flex flex-wrap gap-3 mb-6">
            {project.tech.map((tech) => (
              <Badge
                key={tech}
                variant="secondary"
                className="text-sm px-3 py-1 rounded-full hover:bg-primary hover:text-primary-foreground transition-all duration-200 transform hover:scale-105"
              >
                {tech}
              </Badge>
            ))}
          </div>

          {/* Project Image */}
          {project.image && (
            <div className="rounded-xl overflow-hidden mb-6 border border-muted shadow-md">
              <img
                src={project.image}
                alt={project.title}
                className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 mb-6">
            {project.demoUrl && project.demoUrl !== "#" && (
              <Button
                size="sm"
                className="flex items-center space-x-2 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                onClick={() => window.open(project.demoUrl, '_blank')}
              >
                <ExternalLink className="h-4 w-4" />
                <span>Live Demo</span>
              </Button>
            )}
            {project.githubUrl && (
              <Button
                size="sm"
                variant="outline"
                className="flex items-center space-x-2 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                onClick={() => window.open(project.githubUrl, '_blank')}
              >
                <Github className="h-4 w-4" />
                <span>Code</span>
              </Button>
            )}
          </div>
        </div>

        {/* Engagement Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-muted">
          <div className="flex space-x-8">
            <button
              className={`flex items-center space-x-2 text-sm transition-all duration-200 hover:scale-110 ${
                isLiked ? 'text-red-500' : 'text-muted-foreground hover:text-primary'
              }`}
              onClick={handleLike}
            >
              <Heart className={`h-5 w-5 ${isLiked ? 'fill-current' : ''}`} />
              <span className="font-medium">{likeCount}</span>
            </button>

            <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110">
              <MessageCircle className="h-5 w-5" />
              <span className="font-medium">{project.comments}</span>
            </button>

            <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110">
              <Share className="h-5 w-5" />
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}