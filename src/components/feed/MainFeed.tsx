import ProjectCard from "./ProjectCard";
import ProfileSection from "@/components/sections/ProfileSection";
import GeometricHeader from "@/components/layout/GeometricHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>n, Sparkles, User, Briefcase, BookOpen, FileText, Mail, ArrowLeft, MoreHorizontal, Calendar, MapPin, Link } from "lucide-react";
import heroImage from "@/assets/hero-bg.jpg";

const sampleProjects = [
  {
    id: "1",
    title: "Vibeo - AI-Powered Nightlife Discovery",
    description: "Co-founded and architected an AI-powered mobile app for personalized nightlife video discovery. Engineered pipeline to handle 50+ daily video uploads with real-time analysis using OpenAI Vision Model, Python, GCP Cloud Functions, and Pinecone for vector-based retrieval. Scaled backend to support 25 concurrent users with <100ms response time.",
    image: heroImage,
    tech: ["Flutter", "Python", "OpenAI Vision", "GCP", "Pine<PERSON>e", "<PERSON><PERSON><PERSON><PERSON>", "Docker"],
    githubUrl: "https://github.com/PratikJH153",
    demoUrl: "https://www.vibeo.io",
    likes: 127,
    comments: 23,
    timeAgo: "Current"
  },
  {
    id: "2",
    title: "AI Agent - Code Reviewer",
    description: "Built an AI agent that connects to repositories, parses codebase structure, and understands project context through file analysis. Enabled conversational code review by allowing users to query commit history, merges, and proposed changes via LLM chat interface.",
    image: heroImage,
    tech: ["Python", "LangChain", "OpenAI API", "Git", "Vector DB"],
    githubUrl: "https://github.com/PratikJH153",
    demoUrl: "#",
    likes: 89,
    comments: 15,
    timeAgo: "2 months"
  },
  {
    id: "3",
    title: "Enthem - Social Media App",
    description: "Developed platform enabling users to connect with like-minded peers nearby using Neo4j's proximity-based graph clustering. Implemented features for creating and joining temporary, hyper-personalized chat rooms based on shared interests and location.",
    image: heroImage,
    tech: ["React", "Node.js", "Neo4j", "WebSocket", "MongoDB"],
    githubUrl: "https://github.com/PratikJH153",
    likes: 64,
    comments: 12,
    timeAgo: "4 months"
  },
  {
    id: "4",
    title: "Wajooba LLC - Backend Development",
    description: "Constructed and improved backend features using Node.js, MongoDB, and Neo4j, making data processing 20% faster and enabling users to generate detailed, customized reports. Integrated tools like Airbyte for data syncing and SendGrid for email communication. Improved system performance by 15% through containerization.",
    image: heroImage,
    tech: ["Node.js", "MongoDB", "Neo4j", "Airbyte", "SendGrid", "Docker"],
    githubUrl: "https://github.com/PratikJH153",
    likes: 45,
    comments: 8,
    timeAgo: "1 year"
  }
];

const pinnedProject = {
  id: "pinned",
  title: "ShapeShift Conference - RAG AI Professor",
  description: "Facilitated an RAG AI 'live professor' concept with 3D avatar technology in physical space to reimagine virtual learning. Demonstrated work to leading designers from top multinational firms, receiving recognition for innovation in immersive education. Collaborated with Steelcase Inc. to prototype AI integration in 3D interactive spaces.",
  image: heroImage,
  tech: ["Python", "RAG", "3D Graphics", "AI/ML", "Unity", "LangChain"],
  githubUrl: "https://github.com/PratikJH153",
  demoUrl: "https://pratikjh.netlify.app",
  likes: 156,
  comments: 31,
  timeAgo: "Recent"
};

export default function MainFeed() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Twitter-style Header */}
      <div className="sticky top-0 lg:top-0 bg-black/80 backdrop-blur-md border-b border-border z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-4">
            <ArrowLeft className="h-5 w-5 text-white cursor-pointer lg:hidden" />
            <div>
              <h1 className="text-xl font-bold text-white">unfold</h1>
              <p className="text-sm text-gray-500">838 Tweets</p>
            </div>
          </div>
          <MoreHorizontal className="h-5 w-5 text-white cursor-pointer" />
        </div>
      </div>

      {/* Profile Header with Geometric Pattern */}
      <div className="relative">
        <GeometricHeader />

        {/* Profile Info */}
        <div className="px-4 pb-4">
          {/* Avatar */}
          <div className="relative -mt-16 mb-4">
            <div className="w-32 h-32 bg-gradient-to-br from-primary to-primary/60 rounded-full border-4 border-black flex items-center justify-center text-white font-bold text-4xl">
              PJ
            </div>
          </div>

          {/* Follow Button */}
          <div className="flex justify-end mb-4">
            <Button className="twitter-button-outline px-6 py-2">
              Following
            </Button>
          </div>

          {/* Profile Details */}
          <div className="space-y-3">
            <div>
              <h1 className="text-2xl font-bold text-white">unfold</h1>
              <p className="text-gray-500">@PratikJH153</p>
            </div>

            <p className="text-white text-lg">
              Design more gooder. Creative design agency creating solutions for brands like Solana,
              Facebook, Ripple, Monck Adobe, Figma, & more. Working on @RenewNFT.
            </p>

            <div className="flex items-center space-x-4 text-gray-500 text-sm">
              <div className="flex items-center space-x-1">
                <MapPin className="h-4 w-4" />
                <span>Sarasota, FL</span>
              </div>
              <div className="flex items-center space-x-1">
                <Link className="h-4 w-4" />
                <span className="text-primary">dribbble.com/unfold</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Joined September 2013</span>
              </div>
            </div>

            <div className="flex items-center space-x-6 text-sm">
              <div>
                <span className="text-white font-bold">122</span>
                <span className="text-gray-500 ml-1">Following</span>
              </div>
              <div>
                <span className="text-white font-bold">4,973</span>
                <span className="text-gray-500 ml-1">Followers</span>
              </div>
            </div>

            <p className="text-gray-500 text-sm">
              Followed by Mev Meta World Peace, Cris Birmingham, and 20 others you follow
            </p>
          </div>
        </div>
      </div>

      {/* Twitter-style Tabs */}
      <div className="border-b border-border">
        <div className="flex">
          <button className="flex-1 py-4 text-center font-bold text-white border-b-2 border-primary">
            Tweets
          </button>
          <button className="flex-1 py-4 text-center text-gray-500 hover:bg-white/5 transition-colors duration-200">
            Tweets & replies
          </button>
          <button className="flex-1 py-4 text-center text-gray-500 hover:bg-white/5 transition-colors duration-200">
            Media
          </button>
          <button className="flex-1 py-4 text-center text-gray-500 hover:bg-white/5 transition-colors duration-200">
            Likes
          </button>
        </div>
      </div>

      {/* Content Feed */}
      <div className="divide-y divide-border">
        {/* Pinned Tweet */}
        <div className="p-4 hover:bg-white/5 transition-colors duration-200">
          <div className="flex items-center space-x-2 mb-3">
            <Pin className="h-4 w-4 text-gray-500" />
            <span className="text-gray-500 text-sm">Pinned Tweet</span>
          </div>
          <div className="flex space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
              PJ
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className="font-bold text-white">unfold</span>
                <span className="text-gray-500">@PratikJH153</span>
                <span className="text-gray-500">·</span>
                <span className="text-gray-500">Oct 27</span>
              </div>
              <p className="text-white mb-3">
                We know you're busy and there's a lot to learn out there.
              </p>
              <p className="text-white mb-3">
                Let us know what content you're interested in learning and we'll make sure to create it for you 👇
              </p>
              <div className="bg-gray-900 rounded-2xl p-6 mb-4">
                <h3 className="text-white text-xl font-bold text-center mb-4">
                  We want to hear from you
                </h3>
                <p className="text-gray-400 text-center">
                  What topics would you like us to cover in our upcoming content?
                </p>
              </div>
              <div className="flex items-center justify-between text-gray-500 max-w-md">
                <button className="flex items-center space-x-2 hover:text-primary transition-colors duration-200">
                  <span>💬</span>
                  <span>23</span>
                </button>
                <button className="flex items-center space-x-2 hover:text-green-500 transition-colors duration-200">
                  <span>🔄</span>
                  <span>12</span>
                </button>
                <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200">
                  <span>❤️</span>
                  <span>156</span>
                </button>
                <button className="hover:text-primary transition-colors duration-200">
                  <span>📤</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Regular Tweets - Portfolio Projects */}
        {sampleProjects.map((project, index) => (
          <div key={project.id} className="p-4 hover:bg-white/5 transition-colors duration-200 border-b border-border">
            <div className="flex space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
                PJ
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="font-bold text-white">unfold</span>
                  <span className="text-gray-500">@PratikJH153</span>
                  <span className="text-gray-500">·</span>
                  <span className="text-gray-500">{project.timeAgo}</span>
                </div>
                <p className="text-white mb-3">
                  🚀 Just shipped: <strong>{project.title}</strong>
                </p>
                <p className="text-white mb-3">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-3">
                  {project.tech.map((tech) => (
                    <span key={tech} className="bg-primary/20 text-primary px-2 py-1 rounded text-sm">
                      #{tech}
                    </span>
                  ))}
                </div>
                {project.image && (
                  <div className="rounded-2xl overflow-hidden mb-3">
                    <img src={project.image} alt={project.title} className="w-full h-64 object-cover" />
                  </div>
                )}
                <div className="flex items-center justify-between text-gray-500 max-w-md">
                  <button className="flex items-center space-x-2 hover:text-primary transition-colors duration-200">
                    <span>💬</span>
                    <span>{project.comments}</span>
                  </button>
                  <button className="flex items-center space-x-2 hover:text-green-500 transition-colors duration-200">
                    <span>🔄</span>
                    <span>{Math.floor(project.likes / 10)}</span>
                  </button>
                  <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200">
                    <span>❤️</span>
                    <span>{project.likes}</span>
                  </button>
                  <button className="hover:text-primary transition-colors duration-200">
                    <span>📤</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

    </div>
  );
}