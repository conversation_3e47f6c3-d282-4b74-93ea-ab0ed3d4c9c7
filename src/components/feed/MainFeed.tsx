import ProjectCard from "./ProjectCard";
import ProfileSection from "@/components/sections/ProfileSection";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>n, <PERSON>rk<PERSON>, User, <PERSON>riefcase, BookOpen, FileText, Mail } from "lucide-react";
import heroImage from "@/assets/hero-bg.jpg";

const sampleProjects = [
  {
    id: "1",
    title: "Vibeo - AI-Powered Nightlife Discovery",
    description: "Co-founded and architected an AI-powered mobile app for personalized nightlife video discovery. Engineered pipeline to handle 50+ daily video uploads with real-time analysis using OpenAI Vision Model, Python, GCP Cloud Functions, and Pinecone for vector-based retrieval. Scaled backend to support 25 concurrent users with <100ms response time.",
    image: heroImage,
    tech: ["Flutter", "Python", "OpenAI Vision", "GCP", "Pinecone", "LangChain", "Docker"],
    githubUrl: "https://github.com/PratikJH153",
    demoUrl: "https://www.vibeo.io",
    likes: 127,
    comments: 23,
    timeAgo: "Current"
  },
  {
    id: "2",
    title: "AI Agent - Code Reviewer",
    description: "Built an AI agent that connects to repositories, parses codebase structure, and understands project context through file analysis. Enabled conversational code review by allowing users to query commit history, merges, and proposed changes via LLM chat interface.",
    image: heroImage,
    tech: ["Python", "LangChain", "OpenAI API", "Git", "Vector DB"],
    githubUrl: "https://github.com/PratikJH153",
    demoUrl: "#",
    likes: 89,
    comments: 15,
    timeAgo: "2 months"
  },
  {
    id: "3",
    title: "Enthem - Social Media App",
    description: "Developed platform enabling users to connect with like-minded peers nearby using Neo4j's proximity-based graph clustering. Implemented features for creating and joining temporary, hyper-personalized chat rooms based on shared interests and location.",
    image: heroImage,
    tech: ["React", "Node.js", "Neo4j", "WebSocket", "MongoDB"],
    githubUrl: "https://github.com/PratikJH153",
    likes: 64,
    comments: 12,
    timeAgo: "4 months"
  },
  {
    id: "4",
    title: "Wajooba LLC - Backend Development",
    description: "Constructed and improved backend features using Node.js, MongoDB, and Neo4j, making data processing 20% faster and enabling users to generate detailed, customized reports. Integrated tools like Airbyte for data syncing and SendGrid for email communication. Improved system performance by 15% through containerization.",
    image: heroImage,
    tech: ["Node.js", "MongoDB", "Neo4j", "Airbyte", "SendGrid", "Docker"],
    githubUrl: "https://github.com/PratikJH153",
    likes: 45,
    comments: 8,
    timeAgo: "1 year"
  }
];

const pinnedProject = {
  id: "pinned",
  title: "ShapeShift Conference - RAG AI Professor",
  description: "Facilitated an RAG AI 'live professor' concept with 3D avatar technology in physical space to reimagine virtual learning. Demonstrated work to leading designers from top multinational firms, receiving recognition for innovation in immersive education. Collaborated with Steelcase Inc. to prototype AI integration in 3D interactive spaces.",
  image: heroImage,
  tech: ["Python", "RAG", "3D Graphics", "AI/ML", "Unity", "LangChain"],
  githubUrl: "https://github.com/PratikJH153",
  demoUrl: "https://pratikjh.netlify.app",
  likes: 156,
  comments: 31,
  timeAgo: "Recent"
};

export default function MainFeed() {
  return (
    <div className="flex-1 max-w-4xl mx-auto px-4 lg:px-8">
      {/* Hero Section */}
      <section id="home" className="py-8 lg:py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-6xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-4">
            Pratik Jadhav
          </h1>
          <p className="text-xl lg:text-2xl text-muted-foreground mb-6">
            Creative Technologist | AI/ML Engineer
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
            <span>Chicago, IL</span>
            <span>•</span>
            <span>MS Computer Science @ IIT</span>
            <span>•</span>
            <span>Co-founder & CTO @ Vibeo Inc.</span>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-8">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold flex items-center space-x-3 mb-6">
            <User className="h-8 w-8 text-primary" />
            <span>About Me</span>
          </h2>
        </div>
        <ProfileSection />
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-8">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold flex items-center space-x-3 mb-6">
            <Briefcase className="h-8 w-8 text-primary" />
            <span>Projects & Work</span>
          </h2>
          <p className="text-muted-foreground text-lg">Latest projects and technical experiments</p>
        </div>

        {/* Pinned Project */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-4">
            <Pin className="h-5 w-5 text-primary" />
            <span className="text-lg font-semibold text-primary">Featured Project</span>
          </div>
          <ProjectCard project={pinnedProject} />
        </div>

        {/* What's New Section */}
        <Card className="mb-8 shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-md">
                PJ
              </div>
              <div className="flex-1">
                <textarea
                  placeholder="What AI/ML project are you working on today?"
                  className="w-full bg-transparent text-xl placeholder-muted-foreground border-none outline-none resize-none"
                  rows={3}
                />
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-muted-foreground">
                    Share your latest AI innovation or technical breakthrough
                  </div>
                  <Button
                    disabled
                    className="opacity-50 rounded-xl"
                  >
                    Share Project
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Projects Feed */}
        <div className="space-y-6">
          {sampleProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-8">
          <Button
            variant="outline"
            className="px-8 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            Load More Projects
          </Button>
        </div>
      </section>

      {/* Blog Section */}
      <section id="blog" className="py-8">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold flex items-center space-x-3 mb-6">
            <BookOpen className="h-8 w-8 text-primary" />
            <span>Blog & Insights</span>
          </h2>
        </div>
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground text-lg">
              Blog posts and technical insights coming soon...
            </p>
          </CardContent>
        </Card>
      </section>

      {/* Resume Section */}
      <section id="resume" className="py-8">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold flex items-center space-x-3 mb-6">
            <FileText className="h-8 w-8 text-primary" />
            <span>Resume</span>
          </h2>
        </div>
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground text-lg mb-6">
              Download my latest resume to learn more about my experience and skills.
            </p>
            <Button
              className="px-8 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
              onClick={() => window.open('mailto:<EMAIL>?subject=Resume Request', '_blank')}
            >
              <FileText className="h-4 w-4 mr-2" />
              Request Resume
            </Button>
          </CardContent>
        </Card>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-8 pb-16">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold flex items-center space-x-3 mb-6">
            <Mail className="h-8 w-8 text-primary" />
            <span>Get In Touch</span>
          </h2>
        </div>
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border-0">
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground text-lg mb-6">
              Interested in collaboration or have a project in mind? Let's connect!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="px-8 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              >
                <Mail className="h-4 w-4 mr-2" />
                Email Me
              </Button>
              <Button
                variant="outline"
                className="px-8 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                onClick={() => window.open('https://linkedin.com/in/pratikjh', '_blank')}
              >
                Connect on LinkedIn
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}