@tailwind base;
@tailwind components;
@tailwind utilities;

/* Twitter-like portfolio design system matching reference design */

@layer base {
  :root {
    /* Light theme (fallback) */
    --background: 0 0% 100%;
    --foreground: 210 11% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 11% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 11% 15%;

    /* Twitter blue primary */
    --primary: 203 89% 53%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 203 89% 46%;

    --secondary: 210 20% 96%;
    --secondary-foreground: 210 11% 15%;

    --muted: 210 20% 96%;
    --muted-foreground: 210 6% 46%;

    --accent: 210 20% 96%;
    --accent-foreground: 210 11% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 203 89% 53%;

    --radius: 0.5rem;
  }

  .dark {
    /* Twitter dark theme matching reference */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    /* Dark cards with subtle gray */
    --card: 0 0% 3%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 3%;
    --popover-foreground: 0 0% 100%;

    /* Twitter blue stays consistent */
    --primary: 203 89% 53%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 203 89% 46%;

    /* Dark secondary elements */
    --secondary: 0 0% 8%;
    --secondary-foreground: 0 0% 100%;

    /* Muted dark grays */
    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 8%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Dark borders */
    --border: 0 0% 15%;
    --input: 0 0% 8%;
    --ring: 203 89% 53%;

    /* Sidebar specific colors */
    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 203 89% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 8%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 203 89% 53%;
  }

  /* Geometric header colors for the colorful pattern */
  :root {
    --geo-pink: 330 81% 60%;
    --geo-blue: 203 89% 53%;
    --geo-purple: 271 81% 56%;
    --geo-green: 142 76% 36%;
    --geo-yellow: 48 96% 53%;
    --geo-orange: 25 95% 53%;
    --geo-cyan: 180 78% 60%;
    --geo-red: 348 83% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Force dark theme by default to match reference */
    @apply dark;
  }

  /* Ensure dark theme is applied */
  html {
    @apply dark;
  }
}

/* Custom Twitter-like components */
@layer components {
  .twitter-nav-item {
    @apply flex items-center space-x-4 px-4 py-3 rounded-full text-xl font-normal text-foreground hover:bg-muted/10 transition-colors duration-200 cursor-pointer;
  }

  .twitter-nav-item:hover {
    @apply bg-white/10;
  }

  .twitter-nav-item.active {
    @apply font-bold;
  }

  .geometric-pattern {
    background: linear-gradient(45deg,
      hsl(var(--geo-pink)) 0%,
      hsl(var(--geo-blue)) 25%,
      hsl(var(--geo-purple)) 50%,
      hsl(var(--geo-green)) 75%,
      hsl(var(--geo-yellow)) 100%
    );
  }

  .twitter-card {
    @apply bg-card border border-border rounded-2xl hover:bg-card/80 transition-colors duration-200;
  }

  .twitter-button {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-full font-bold transition-colors duration-200;
  }

  .twitter-button-outline {
    @apply border border-border text-foreground hover:bg-muted/10 rounded-full font-bold transition-colors duration-200;
  }
}