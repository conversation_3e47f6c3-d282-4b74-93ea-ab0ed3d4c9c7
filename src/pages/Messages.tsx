import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, Github, Linkedin, ExternalLink, Send } from "lucide-react";

const Messages: React.FC = () => {
  const conversations = [
    {
      id: "1",
      name: "Vibeo Team",
      handle: "@vibeo_team",
      lastMessage: "Great work on the AI pipeline optimization! The 50+ daily uploads are processing smoothly.",
      timestamp: "2h",
      unread: true,
      avatar: "V"
    },
    {
      id: "2",
      name: "IIT Research Lab",
      handle: "@iit_research",
      lastMessage: "Your thesis on AI-powered code analysis was impressive. Would love to discuss collaboration opportunities.",
      timestamp: "1d",
      unread: false,
      avatar: "I"
    },
    {
      id: "3",
      name: "ShapeShift Conference",
      handle: "@shapeshiftconf",
      lastMessage: "Thank you for the amazing RAG AI demonstration! The audience loved the 3D avatar integration.",
      timestamp: "1w",
      unread: false,
      avatar: "S"
    },
    {
      id: "4",
      name: "<PERSON> Recruiter",
      handle: "@techtalent",
      lastMessage: "<PERSON> Pratik! Saw your work with AI/ML. We have some exciting opportunities that might interest you.",
      timestamp: "2w",
      unread: false,
      avatar: "T"
    }
  ];

  const ContactCard = ({ icon: Icon, title, description, action, link }: {
    icon: React.ElementType;
    title: string;
    description: string;
    action: string;
    link: string;
  }) => (
    <Card className="twitter-card bg-gray-900 border-gray-800 hover:bg-gray-800/50 transition-colors duration-200">
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
            <Icon className="h-6 w-6 text-primary" />
          </div>
          <div className="flex-1">
            <h3 className="text-white font-bold text-lg mb-2">{title}</h3>
            <p className="text-gray-400 mb-4">{description}</p>
            <Button 
              className="twitter-button"
              onClick={() => window.open(link, '_blank')}
            >
              <Icon className="h-4 w-4 mr-2" />
              {action}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h2 className="text-xl font-bold text-white mb-2">Messages</h2>
        <p className="text-gray-500">Connect and collaborate</p>
      </div>

      {/* Quick Contact Section */}
      <div className="p-4 space-y-4">
        <h3 className="text-lg font-bold text-white mb-4">Get in Touch</h3>
        
        <ContactCard
          icon={Mail}
          title="Email"
          description="For professional inquiries, collaboration opportunities, or just to say hello"
          action="Send Email"
          link="mailto:<EMAIL>"
        />

        <ContactCard
          icon={Linkedin}
          title="LinkedIn"
          description="Connect with me professionally and see my career journey"
          action="Connect on LinkedIn"
          link="https://linkedin.com/in/pratikjh"
        />

        <ContactCard
          icon={Github}
          title="GitHub"
          description="Explore my code, contribute to projects, or start a technical discussion"
          action="View GitHub"
          link="https://github.com/PratikJH153"
        />

        <ContactCard
          icon={ExternalLink}
          title="Vibeo"
          description="Check out our AI-powered nightlife discovery platform"
          action="Visit Vibeo"
          link="https://www.vibeo.io"
        />
      </div>

      {/* Recent Conversations */}
      <div className="border-t border-border">
        <div className="p-4">
          <h3 className="text-lg font-bold text-white mb-4">Recent Conversations</h3>
        </div>
        
        <div className="divide-y divide-border">
          {conversations.map((conversation) => (
            <div key={conversation.id} className="p-4 hover:bg-white/5 transition-colors duration-200 cursor-pointer">
              <div className="flex space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
                  {conversation.avatar}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-bold text-white">{conversation.name}</span>
                      <span className="text-gray-500 text-sm">{conversation.handle}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500 text-sm">{conversation.timestamp}</span>
                      {conversation.unread && (
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                      )}
                    </div>
                  </div>
                  <p className="text-gray-400 truncate">{conversation.lastMessage}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="p-4 border-t border-border">
        <Card className="twitter-card bg-gradient-to-r from-primary/20 to-primary/10 border-primary/30">
          <CardContent className="p-6 text-center">
            <h3 className="text-white font-bold text-xl mb-2">Let's Build Something Amazing Together</h3>
            <p className="text-gray-300 mb-4">
              Whether you're interested in AI/ML, want to collaborate on a project, or just want to chat about technology, I'd love to hear from you!
            </p>
            <Button 
              className="twitter-button"
              onClick={() => window.open('mailto:<EMAIL>?subject=Let\'s Collaborate!', '_blank')}
            >
              <Send className="h-4 w-4 mr-2" />
              Start a Conversation
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Messages;
