import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Bookmark, ExternalLink, Heart, MessageCircle, Repeat2, Calendar } from "lucide-react";

const Bookmarks: React.FC = () => {
  const bookmarkedContent = [
    {
      id: "1",
      type: "article",
      title: "The Future of AI in Software Development",
      description: "An insightful piece about how AI is transforming the way we write, review, and deploy code. Particularly relevant to my work on AI-powered code review systems.",
      url: "https://example.com/ai-software-dev",
      savedDate: "2 days ago",
      tags: ["AI", "Software Development", "Code Review"],
      author: "Tech Insights",
      likes: 234,
      bookmarked: true
    },
    {
      id: "2",
      type: "research",
      title: "RAG Systems in Production: Lessons Learned",
      description: "Comprehensive analysis of implementing Retrieval-Augmented Generation systems at scale. Great insights for my ShapeShift Conference project.",
      url: "https://example.com/rag-production",
      savedDate: "1 week ago",
      tags: ["RAG", "AI", "Machine Learning", "Production"],
      author: "AI Research Lab",
      likes: 189,
      bookmarked: true
    },
    {
      id: "3",
      type: "tutorial",
      title: "Building Real-time Video Processing Pipelines",
      description: "Step-by-step guide on creating efficient video processing systems. Directly applicable to Vibeo's video upload and analysis pipeline.",
      url: "https://example.com/video-processing",
      savedDate: "2 weeks ago",
      tags: ["Video Processing", "Real-time", "Pipeline", "GCP"],
      author: "Cloud Architecture",
      likes: 156,
      bookmarked: true
    },
    {
      id: "4",
      type: "inspiration",
      title: "From Idea to Startup: An AI Founder's Journey",
      description: "Motivational story about building an AI startup from scratch. Resonates with my experience co-founding Vibeo Inc.",
      url: "https://example.com/startup-journey",
      savedDate: "3 weeks ago",
      tags: ["Startup", "AI", "Entrepreneurship", "Founder"],
      author: "Startup Stories",
      likes: 298,
      bookmarked: true
    },
    {
      id: "5",
      type: "technical",
      title: "Vector Databases: The Backbone of Modern AI",
      description: "Deep dive into vector databases and their role in AI applications. Essential reading for anyone working with embeddings and similarity search.",
      url: "https://example.com/vector-databases",
      savedDate: "1 month ago",
      tags: ["Vector DB", "Pinecone", "Embeddings", "AI"],
      author: "Database Weekly",
      likes: 167,
      bookmarked: true
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'article': return '📰';
      case 'research': return '🔬';
      case 'tutorial': return '📚';
      case 'inspiration': return '💡';
      case 'technical': return '⚙️';
      default: return '📄';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'article': return 'bg-blue-500/20 text-blue-400';
      case 'research': return 'bg-purple-500/20 text-purple-400';
      case 'tutorial': return 'bg-green-500/20 text-green-400';
      case 'inspiration': return 'bg-yellow-500/20 text-yellow-400';
      case 'technical': return 'bg-red-500/20 text-red-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const BookmarkCard = ({ item }: { item: typeof bookmarkedContent[0] }) => (
    <div className="border-b border-border p-4 hover:bg-white/5 transition-colors duration-200">
      <div className="flex space-x-3">
        <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
          PJ
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-bold text-white">unfold</span>
            <span className="text-gray-500">bookmarked</span>
            <span className="text-gray-500">·</span>
            <span className="text-gray-500">{item.savedDate}</span>
            <span className="text-lg">{getTypeIcon(item.type)}</span>
          </div>
          
          <div className="mb-3">
            <div className="flex items-center space-x-2 mb-2">
              <span className={`px-2 py-1 rounded-full text-xs font-bold ${getTypeColor(item.type)}`}>
                {item.type.toUpperCase()}
              </span>
              <span className="text-gray-500 text-sm">by {item.author}</span>
            </div>
            
            <h3 className="text-white font-bold text-lg mb-2">{item.title}</h3>
            <p className="text-gray-300 leading-relaxed mb-3">{item.description}</p>
            
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-3">
              {item.tags.map((tag) => (
                <span key={tag} className="bg-primary/20 text-primary px-2 py-1 rounded-full text-sm">
                  #{tag}
                </span>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mb-3">
              <Button 
                variant="outline" 
                size="sm" 
                className="twitter-button-outline"
                onClick={() => window.open(item.url, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Read Article
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="twitter-button-outline"
              >
                <Bookmark className="h-4 w-4 mr-2 fill-current" />
                Saved
              </Button>
            </div>
          </div>

          {/* Engagement */}
          <div className="flex items-center justify-between text-gray-500 max-w-md">
            <button className="flex items-center space-x-2 hover:text-primary transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-primary/10">
                <MessageCircle className="h-4 w-4" />
              </div>
              <span>Share thoughts</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-green-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-green-500/10">
                <Repeat2 className="h-4 w-4" />
              </div>
              <span>Retweet</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-red-500/10">
                <Heart className="h-4 w-4" />
              </div>
              <span>{item.likes}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h2 className="text-xl font-bold text-white mb-2">Bookmarks</h2>
        <p className="text-gray-500">Your saved articles, research, and inspiration</p>
      </div>

      {/* Stats */}
      <div className="p-4 border-b border-border">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{bookmarkedContent.length}</div>
            <div className="text-sm text-gray-500">Total Saved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">3</div>
            <div className="text-sm text-gray-500">This Week</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">5</div>
            <div className="text-sm text-gray-500">Categories</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">12</div>
            <div className="text-sm text-gray-500">This Month</div>
          </div>
        </div>
      </div>

      {/* Bookmarked Content */}
      <div className="divide-y divide-border">
        {bookmarkedContent.map((item) => (
          <BookmarkCard key={item.id} item={item} />
        ))}
      </div>

      {/* Load More */}
      <div className="p-4 text-center">
        <Button variant="outline" className="twitter-button-outline">
          Load More Bookmarks
        </Button>
      </div>
    </div>
  );
};

export default Bookmarks;
