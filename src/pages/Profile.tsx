import React from 'react';
import GeometricHeader from "@/components/layout/GeometricHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, MapPin, Link, Heart, MessageCircle, Repeat2 } from "lucide-react";

const Profile: React.FC = () => {
  const profileTweets = [
    {
      id: "1",
      content: "🎓 Just completed my MS in Computer Science @ IIT! The journey from algorithms to real-world applications has been incredible. Ready to build the future with AI/ML! #Education #ComputerScience #AI",
      timestamp: "6mo",
      likes: 234,
      retweets: 45,
      comments: 28,
      type: "milestone"
    },
    {
      id: "2",
      content: "🚀 Excited to announce that I'm now Co-founder & CTO @ Vibeo Inc.! We're revolutionizing nightlife discovery with AI. From idea to 50+ daily video uploads with <100ms response time. The future is here! #Startup #AI #CTO",
      timestamp: "8mo",
      likes: 189,
      retweets: 67,
      comments: 42,
      type: "career"
    },
    {
      id: "3",
      content: "💡 My journey into AI started with a simple question: 'How can machines understand context like humans?' Today, I'm building systems that do exactly that. Every algorithm tells a story. #AI #MachineLearning #Journey",
      timestamp: "1y",
      likes: 156,
      retweets: 23,
      comments: 19,
      type: "reflection"
    },
    {
      id: "4",
      content: "🏆 Honored to present at ShapeShift Conference! Demonstrated RAG AI 'live professor' with 3D avatars to leading designers from multinational firms. Innovation happens when technology meets creativity! #Innovation #AI #3D",
      timestamp: "1y",
      likes: 298,
      retweets: 89,
      comments: 56,
      type: "achievement"
    },
    {
      id: "5",
      content: "🌟 From Chicago to the world - building AI solutions that matter. Every line of code is a step toward a more intelligent future. What started as curiosity became my life's work. #Chicago #AI #Purpose",
      timestamp: "1y",
      likes: 167,
      retweets: 34,
      comments: 22,
      type: "personal"
    }
  ];

  const getTypeEmoji = (type: string) => {
    switch (type) {
      case 'milestone': return '🎓';
      case 'career': return '🚀';
      case 'reflection': return '💡';
      case 'achievement': return '🏆';
      case 'personal': return '🌟';
      default: return '📝';
    }
  };

  const TweetCard = ({ tweet }: { tweet: typeof profileTweets[0] }) => (
    <div className="border-b border-border p-4 hover:bg-white/5 transition-colors duration-200">
      <div className="flex space-x-3">
        <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
          PJ
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-bold text-white">unfold</span>
            <span className="text-gray-500">@PratikJH153</span>
            <span className="text-gray-500">·</span>
            <span className="text-gray-500">{tweet.timestamp}</span>
            <span className="text-lg">{getTypeEmoji(tweet.type)}</span>
          </div>
          <p className="text-white mb-3 leading-relaxed">{tweet.content}</p>
          <div className="flex items-center justify-between text-gray-500 max-w-md">
            <button className="flex items-center space-x-2 hover:text-primary transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-primary/10">
                <MessageCircle className="h-4 w-4" />
              </div>
              <span>{tweet.comments}</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-green-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-green-500/10">
                <Repeat2 className="h-4 w-4" />
              </div>
              <span>{tweet.retweets}</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-red-500/10">
                <Heart className="h-4 w-4" />
              </div>
              <span>{tweet.likes}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen">
      {/* Profile Header with Geometric Pattern */}
      <div className="relative">
        <GeometricHeader />
        
        {/* Profile Info */}
        <div className="px-4 pb-4">
          {/* Avatar */}
          <div className="relative -mt-16 mb-4">
            <div className="w-32 h-32 bg-gradient-to-br from-primary to-primary/60 rounded-full border-4 border-black flex items-center justify-center text-white font-bold text-4xl">
              PJ
            </div>
          </div>

          {/* Follow Button */}
          <div className="flex justify-end mb-4">
            <Button className="twitter-button-outline px-6 py-2">
              Following
            </Button>
          </div>

          {/* Profile Details */}
          <div className="space-y-3">
            <div>
              <h1 className="text-2xl font-bold text-white">unfold</h1>
              <p className="text-gray-500">@PratikJH153</p>
            </div>
            
            <p className="text-white text-lg">
              Creative Technologist & AI/ML Engineer. Co-founder & CTO @ Vibeo Inc. 
              Building the future with intelligent systems. MS Computer Science @ IIT.
              Passionate about AI, innovation, and creating solutions that matter.
            </p>

            <div className="flex items-center space-x-4 text-gray-500 text-sm">
              <div className="flex items-center space-x-1">
                <MapPin className="h-4 w-4" />
                <span>Chicago, IL</span>
              </div>
              <div className="flex items-center space-x-1">
                <Link className="h-4 w-4" />
                <span className="text-primary">vibeo.io</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Joined September 2020</span>
              </div>
            </div>

            <div className="flex items-center space-x-6 text-sm">
              <div>
                <span className="text-white font-bold">122</span>
                <span className="text-gray-500 ml-1">Following</span>
              </div>
              <div>
                <span className="text-white font-bold">4,973</span>
                <span className="text-gray-500 ml-1">Followers</span>
              </div>
            </div>

            <p className="text-gray-500 text-sm">
              Followed by leading AI researchers, tech innovators, and 20 others you follow
            </p>
          </div>
        </div>
      </div>

      {/* Profile Tabs */}
      <div className="border-b border-border">
        <div className="flex">
          <button className="flex-1 py-4 text-center font-bold text-white border-b-2 border-primary">
            Tweets
          </button>
          <button className="flex-1 py-4 text-center text-gray-500 hover:bg-white/5 transition-colors duration-200">
            Tweets & replies
          </button>
          <button className="flex-1 py-4 text-center text-gray-500 hover:bg-white/5 transition-colors duration-200">
            Media
          </button>
          <button className="flex-1 py-4 text-center text-gray-500 hover:bg-white/5 transition-colors duration-200">
            Likes
          </button>
        </div>
      </div>

      {/* Profile Timeline */}
      <div className="divide-y divide-border">
        {profileTweets.map((tweet) => (
          <TweetCard key={tweet.id} tweet={tweet} />
        ))}
      </div>
    </div>
  );
};

export default Profile;
