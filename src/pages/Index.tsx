import Sidebar from "@/components/layout/Sidebar";
import RightSidebar from "@/components/layout/RightSidebar";
import MainFeed from "@/components/feed/MainFeed";
import MobileNav from "@/components/layout/MobileNav";

const Index = () => {
  return (
    <div className="min-h-screen bg-black">
      {/* Mobile Navigation */}
      <MobileNav />

      {/* Desktop Layout - Three Column Twitter-like */}
      <div className="hidden lg:flex">
        {/* Left Sidebar - Navigation */}
        <Sidebar />

        {/* Main Content Area - Center column */}
        <div className="flex-1 ml-64 xl:mr-80 border-x border-border">
          <MainFeed />
        </div>

        {/* Right Sidebar - Suggestions and trending */}
        <RightSidebar />
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden pt-16">
        <div className="w-full">
          <MainFeed />
        </div>
      </div>
    </div>
  );
};

export default Index;
