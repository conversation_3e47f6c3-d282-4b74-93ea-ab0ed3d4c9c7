import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Pin, Heart, MessageCircle, Repeat2, Share } from "lucide-react";

const Home: React.FC = () => {
  const tweets = [
    {
      id: "1",
      content: "🚀 Just launched my latest AI project! Built an intelligent code reviewer that understands context and provides meaningful feedback. The future of development is here! #AI #MachineLearning #CodeReview",
      timestamp: "2h",
      likes: 127,
      retweets: 23,
      comments: 15,
      isPinned: true,
    },
    {
      id: "2", 
      content: "Working on something exciting with @Vibeo - AI-powered nightlife discovery is changing how people explore their cities. 50+ daily video uploads, real-time analysis, <100ms response time! 🎉",
      timestamp: "1d",
      likes: 89,
      retweets: 12,
      comments: 8,
    },
    {
      id: "3",
      content: "Just finished an amazing session at ShapeShift Conference! Demonstrated our RAG AI 'live professor' with 3D avatar technology. The intersection of AI and immersive learning is incredible! 🤖✨",
      timestamp: "3d", 
      likes: 156,
      retweets: 31,
      comments: 22,
    },
    {
      id: "4",
      content: "Reflecting on my journey from MS Computer Science @ IIT to Co-founder & CTO @ Vibeo Inc. Every line of code, every algorithm, every late night debugging session led to this moment. Keep building! 💻",
      timestamp: "1w",
      likes: 203,
      retweets: 45,
      comments: 28,
    },
    {
      id: "5",
      content: "Tech stack update: React + TypeScript for frontend, Python + LangChain for AI backend, Docker for deployment. The modern developer's toolkit keeps evolving! What's your current stack? 🛠️",
      timestamp: "1w",
      likes: 78,
      retweets: 19,
      comments: 34,
    }
  ];

  const TweetCard = ({ tweet, index }: { tweet: typeof tweets[0], index: number }) => (
    <motion.div
      className="border-b border-border p-4 hover:bg-white/5 transition-colors duration-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
    >
      {tweet.isPinned && (
        <motion.div
          className="flex items-center space-x-2 mb-3 text-gray-500"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Pin className="h-4 w-4" />
          <span className="text-sm">Pinned Tweet</span>
        </motion.div>
      )}
      <div className="flex space-x-3">
        <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
          PJ
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-bold text-white">unfold</span>
            <span className="text-gray-500">@PratikJH153</span>
            <span className="text-gray-500">·</span>
            <span className="text-gray-500">{tweet.timestamp}</span>
          </div>
          <p className="text-white mb-3 leading-relaxed">{tweet.content}</p>
          <div className="flex items-center justify-between text-gray-500 max-w-md">
            <button className="flex items-center space-x-2 hover:text-primary transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-primary/10">
                <MessageCircle className="h-4 w-4" />
              </div>
              <span>{tweet.comments}</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-green-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-green-500/10">
                <Repeat2 className="h-4 w-4" />
              </div>
              <span>{tweet.retweets}</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-red-500/10">
                <Heart className="h-4 w-4" />
              </div>
              <span>{tweet.likes}</span>
            </button>
            <button className="hover:text-primary transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-primary/10">
                <Share className="h-4 w-4" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen">
      {/* What's happening section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="twitter-card bg-gray-900 border-gray-800 m-4">
          <CardContent className="p-4">
            <div className="flex space-x-3">
              <motion.div
                className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                PJ
              </motion.div>
              <div className="flex-1">
                <textarea
                  placeholder="What's happening in your tech journey?"
                  className="w-full bg-transparent text-xl placeholder-gray-500 border-none outline-none resize-none text-white"
                  rows={3}
                />
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-500">
                    Share your latest project, insight, or breakthrough
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button className="twitter-button px-6 py-2">
                      Tweet
                    </Button>
                  </motion.div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Timeline */}
      <div className="divide-y divide-border">
        {tweets.map((tweet, index) => (
          <TweetCard key={tweet.id} tweet={tweet} index={index} />
        ))}
      </div>
    </div>
  );
};

export default Home;
