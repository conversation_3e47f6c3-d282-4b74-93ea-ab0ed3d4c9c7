import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, Github, Globe, Heart, MessageCircle, Repeat2 } from "lucide-react";
import heroImage from "@/assets/hero-bg.jpg";

const Explore: React.FC = () => {
  const projects = [
    {
      id: "1",
      title: "Vibeo - AI-Powered Nightlife Discovery",
      description: "Co-founded and architected an AI-powered mobile app for personalized nightlife video discovery. Engineered pipeline to handle 50+ daily video uploads with real-time analysis using OpenAI Vision Model, Python, GCP Cloud Functions, and Pinecone for vector-based retrieval.",
      image: heroImage,
      tech: ["Flutter", "Python", "OpenAI Vision", "GCP", "Pinecone", "LangChain", "Docker"],
      githubUrl: "https://github.com/PratikJH153",
      demoUrl: "https://www.vibeo.io",
      likes: 127,
      comments: 23,
      retweets: 12,
      status: "Current",
      featured: true,
    },
    {
      id: "2",
      title: "AI Agent - Code Reviewer",
      description: "Built an AI agent that connects to repositories, parses codebase structure, and understands project context through file analysis. Enabled conversational code review by allowing users to query commit history, merges, and proposed changes via LLM chat interface.",
      image: heroImage,
      tech: ["Python", "LangChain", "OpenAI API", "Git", "Vector DB"],
      githubUrl: "https://github.com/PratikJH153",
      likes: 89,
      comments: 15,
      retweets: 8,
      status: "2 months ago",
    },
    {
      id: "3",
      title: "Enthem - Social Media App",
      description: "Developed platform enabling users to connect with like-minded peers nearby using Neo4j's proximity-based graph clustering. Implemented features for creating and joining temporary, hyper-personalized chat rooms based on shared interests and location.",
      image: heroImage,
      tech: ["React", "Node.js", "Neo4j", "WebSocket", "MongoDB"],
      githubUrl: "https://github.com/PratikJH153",
      likes: 64,
      comments: 12,
      retweets: 5,
      status: "4 months ago",
    },
    {
      id: "4",
      title: "ShapeShift Conference - RAG AI Professor",
      description: "Facilitated an RAG AI 'live professor' concept with 3D avatar technology in physical space to reimagine virtual learning. Demonstrated work to leading designers from top multinational firms, receiving recognition for innovation in immersive education.",
      image: heroImage,
      tech: ["Python", "RAG", "3D Graphics", "AI/ML", "Unity", "LangChain"],
      githubUrl: "https://github.com/PratikJH153",
      demoUrl: "https://pratikjh.netlify.app",
      likes: 156,
      comments: 31,
      retweets: 18,
      status: "Recent",
    },
  ];

  const ProjectCard = ({ project, index }: { project: typeof projects[0], index: number }) => (
    <motion.div
      className="border-b border-border p-4 hover:bg-white/5 transition-colors duration-200"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.15 }}
      whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
    >
      <div className="flex space-x-3">
        <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold">
          PJ
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-bold text-white">unfold</span>
            <span className="text-gray-500">@PratikJH153</span>
            <span className="text-gray-500">·</span>
            <span className="text-gray-500">{project.status}</span>
            {project.featured && (
              <span className="bg-primary/20 text-primary px-2 py-1 rounded-full text-xs font-bold">
                FEATURED
              </span>
            )}
          </div>
          
          <div className="mb-3">
            <h3 className="text-white font-bold text-lg mb-2">🚀 {project.title}</h3>
            <p className="text-white leading-relaxed mb-3">{project.description}</p>
            
            {/* Tech Stack */}
            <div className="flex flex-wrap gap-2 mb-3">
              {project.tech.map((tech) => (
                <span key={tech} className="bg-primary/20 text-primary px-2 py-1 rounded-full text-sm">
                  #{tech}
                </span>
              ))}
            </div>

            {/* Project Image */}
            {project.image && (
              <div className="rounded-2xl overflow-hidden mb-3 border border-border">
                <img src={project.image} alt={project.title} className="w-full h-64 object-cover" />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3 mb-3">
              {project.githubUrl && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    className="twitter-button-outline"
                    onClick={() => window.open(project.githubUrl, '_blank')}
                  >
                    <Github className="h-4 w-4 mr-2" />
                    Code
                  </Button>
                </motion.div>
              )}
              {project.demoUrl && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    size="sm"
                    className="twitter-button"
                    onClick={() => window.open(project.demoUrl, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Live Demo
                  </Button>
                </motion.div>
              )}
            </div>
          </div>

          {/* Engagement */}
          <div className="flex items-center justify-between text-gray-500 max-w-md">
            <button className="flex items-center space-x-2 hover:text-primary transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-primary/10">
                <MessageCircle className="h-4 w-4" />
              </div>
              <span>{project.comments}</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-green-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-green-500/10">
                <Repeat2 className="h-4 w-4" />
              </div>
              <span>{project.retweets}</span>
            </button>
            <button className="flex items-center space-x-2 hover:text-red-500 transition-colors duration-200 group">
              <div className="p-2 rounded-full group-hover:bg-red-500/10">
                <Heart className="h-4 w-4" />
              </div>
              <span>{project.likes}</span>
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen">
      {/* Header */}
      <motion.div
        className="p-4 border-b border-border"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-xl font-bold text-white mb-2">Explore Projects</h2>
        <p className="text-gray-500">Discover my latest work and technical experiments</p>
      </motion.div>

      {/* Projects Timeline */}
      <div className="divide-y divide-border">
        {projects.map((project, index) => (
          <ProjectCard key={project.id} project={project} index={index} />
        ))}
      </div>
    </div>
  );
};

export default Explore;
